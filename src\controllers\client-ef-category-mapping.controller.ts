import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {ClientEfCategoryMapping} from '../models';
import {ClientEfCategoryMappingRepository, NewEfStdRepository, NewEfSubcategory1Repository} from '../repositories';

export class ClientEfCategoryMappingController {
  constructor(
    @repository(ClientEfCategoryMappingRepository)
    public clientEfCategoryMappingRepository: ClientEfCategoryMappingRepository,
    @repository(NewEfSubcategory1Repository) protected newEfSubcategory1Repository: NewEfSubcategory1Repository,
    @repository(NewEfStdRepository) protected newEfStdRepository: NewEfStdRepository,
  ) { }

  @post('/client-ef-category-mappings', {
    responses: {
      '200': {
        description: 'ClientEfCategoryMapping model instance',
        content: {'application/json': {schema: getModelSchemaRef(ClientEfCategoryMapping)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryMapping, {
            title: 'NewClientEfCategoryMapping',
            exclude: ['id'],
          }),
        },
      },
    })
    clientEfCategoryMapping: Omit<ClientEfCategoryMapping, 'id'>,
  ): Promise<ClientEfCategoryMapping> {
    return this.clientEfCategoryMappingRepository.create(clientEfCategoryMapping);
  }

  @get('/client-ef-category-mappings/count', {
    responses: {
      '200': {
        description: 'ClientEfCategoryMapping model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(ClientEfCategoryMapping) where?: Where<ClientEfCategoryMapping>,
  ): Promise<Count> {
    return this.clientEfCategoryMappingRepository.count(where);
  }

  @get('/client-ef-category-mappings', {
    responses: {
      '200': {
        description: 'Array of ClientEfCategoryMapping model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ClientEfCategoryMapping, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(ClientEfCategoryMapping) filter?: Filter<ClientEfCategoryMapping>,
  ): Promise<ClientEfCategoryMapping[]> {
    return this.clientEfCategoryMappingRepository.find(filter);
  }

  @patch('/client-ef-category-mappings', {
    responses: {
      '200': {
        description: 'ClientEfCategoryMapping PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryMapping, {partial: true}),
        },
      },
    })
    clientEfCategoryMapping: ClientEfCategoryMapping,
    @param.where(ClientEfCategoryMapping) where?: Where<ClientEfCategoryMapping>,
  ): Promise<Count> {
    return this.clientEfCategoryMappingRepository.updateAll(clientEfCategoryMapping, where);
  }

  @get('/client-ef-category-mappings/{id}', {
    responses: {
      '200': {
        description: 'ClientEfCategoryMapping model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ClientEfCategoryMapping, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ClientEfCategoryMapping, {exclude: 'where'}) filter?: FilterExcludingWhere<ClientEfCategoryMapping>
  ): Promise<ClientEfCategoryMapping> {
    return this.clientEfCategoryMappingRepository.findById(id, filter);
  }

  @patch('/client-ef-category-mappings/{id}', {
    responses: {
      '204': {
        description: 'ClientEfCategoryMapping PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryMapping, {partial: true}),
        },
      },
    })
    clientEfCategoryMapping: ClientEfCategoryMapping,
  ): Promise<void> {
    await this.clientEfCategoryMappingRepository.updateById(id, clientEfCategoryMapping);
  }

  @put('/client-ef-category-mappings/{id}', {
    responses: {
      '204': {
        description: 'ClientEfCategoryMapping PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() clientEfCategoryMapping: ClientEfCategoryMapping,
  ): Promise<void> {
    await this.clientEfCategoryMappingRepository.replaceById(id, clientEfCategoryMapping);
  }

  @del('/client-ef-category-mappings/{id}', {
    responses: {
      '204': {
        description: 'ClientEfCategoryMapping DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.clientEfCategoryMappingRepository.deleteById(id);
  }
  @get('/user-profiles/{id}/client-ef-category-mappings-custom')
  @response(200, {
    description: 'Retrieve hierarchical category data for the user profile',
  })
  async getClientEfCategoryMappingsCustom(
    @param.path.number('id') userProfileId: number
  ): Promise<any> {
    // Define a type for the result structure
    type SubCategoryResult = {
      subCategory1: string;
      subCategory2: string;
      subCategory3: string;
      subCategory4: string;
    };

    // Define a type for the extended assignment
    type ExtendedAssignment = ClientEfCategoryMapping & {
      hierarchicalData?: SubCategoryResult[];
    };

    // Fetch assignments for the user
    const assignments: ExtendedAssignment[] = await this.clientEfCategoryMappingRepository.find({
      where: {userProfileId}, include: ['efGhgCat', 'efStandard', 'efCategory', 'efGhgSubCat']
    });
    let result: any = []
    let reqStds = Array.from(new Set(assignments.map(i => i.efStandardId).filter(x => x)))
    let reqCats = Array.from(new Set(assignments.map(i => i.efCategoryId).filter(x => x)))

    // Process each assignment to fetch and add hierarchical data
    const emissionsLibrary = await this.newEfStdRepository.find({
      where: {
        id: {inq: reqStds},
      },
      include: [
        {
          relation: "newEfDates",
          scope: {
            include: [
              {
                relation: "newEfs",
                scope: {
                  where: {
                    category: {inq: reqCats},
                  },
                  include: [
                    {
                      relation: "newEfItems",

                    },
                  ],
                },
              },
            ],
          },
        },
      ],
    })
    const flattenEFLibrary = emissionsLibrary.flatMap((entry) => {
      const newEfDates = entry.newEfDates ?? [];
      return newEfDates.flatMap((dateEntry) => {
        const newEfs = dateEntry.newEfs ?? [];
        return newEfs.flatMap((efEntry) => {
          const newEfItems = efEntry.newEfItems ?? [];
          return newEfItems.map((item) => {
            const {
              subcategory1,
              subcategory2,
              subcategory3,
              subcategory4,
              ...rest
            } = item;

            // Construct efId dynamically
            const subcategories = [subcategory1, subcategory2, subcategory3, subcategory4]
              .filter((subcat) => subcat != null && subcat !== 0)
              .map((subcat) => subcat) // Assuming each subcat has an `id`
              .join('-');

            const efId = `${entry.id}-${efEntry.category}-${subcategories}`;
            const item_ = JSON.parse(JSON.stringify(item));
            return {
              dateId: dateEntry.id,
              startDate: dateEntry?.start ? DateTime.fromISO(dateEntry.start, {zone: "Asia/Calcutta"}).toFormat('LLL-yyyy') : '-',
              endDate: dateEntry.end === null ? 'Present' : dateEntry?.end ? DateTime.fromISO(dateEntry.end, {zone: "Asia/Calcutta"}).toFormat('LLL-yyyy') : '-',
              efId,
              methodology: item?.methodology || '-',
              co2e: item.co2e,
              co2: item.co2,
              ch4: item.ch4,
              n2o: item.n2o,
              additionalEFValue1: item?.other1 ?? 0,
              additionalEFValue2: item?.other2 ?? 0,
              additionalEFValue3: item?.other3 ?? 0,
              additionalEFValue4: item?.other4 ?? 0,

            };
          });
        });
      });
    });

    // Loop through assignments using for...of to await asynchronous operations
    for (const assignment of assignments) {
      const {efStandardId, efCategoryId} = assignment;

      // Fetch hierarchical data for the efCategoryId
      const subCategory = await this.newEfSubcategory1Repository.find({
        where: {newEfCategoryId: efCategoryId},
        include: [
          {
            relation: 'newEfSubcategory2s',
            scope: {
              include: [
                {
                  relation: 'newEfSubcategory3s',
                  scope: {include: ['newEfSubcategory4s']},
                },
              ],
            },
          },
        ],
      });


      // Check if subCategory data is fetched successfully
      if (subCategory && subCategory.length > 0) {
        const selectedIds = assignment?.selected_ids || [];

        // Ensure the hierarchicalData is properly set on the assignment
        const hierarchicalData = await this.getHierarchicalDataForSelectedIds(subCategory, selectedIds);
        const hierarchy: any[] = [];

        for (const data of hierarchicalData) {
          // Find all matching efLib entries
          const matches = flattenEFLibrary.filter(
            efLib => efLib.efId === (efStandardId + '-' + efCategoryId + '-' + data.hierarchyId)
          );

          if (matches.length > 0) {
            // Push a merged object for each match
            for (const match of matches) {
              hierarchy.push({
                ...data,
                ...match,
              });
            }
          } else {

            // No match found, push data with a placeholder for efLib
            hierarchy.push({
              ...data // Or any placeholder object
            });
          }
        }

        result.push({...assignment, hierarchicalData: hierarchy});
        // Add hierarchicalData to the assignment object
        assignment.hierarchicalData = hierarchy;
      } else {
        // If no subCategory found, set hierarchicalData to an empty array
        assignment.hierarchicalData = [];
      }
    }

    // Return the assignments with hierarchical data
    return result;
  }  // Function to merge hierarchical data for multiple selected IDs
  async getHierarchicalDataForSelectedIds(subCategory: any[], selectedIds: string[]) {
    const traverse = (category: any, currentLevel: number, currentPath: any, hierarchyPath: string) => {

      const levelKey = `subCategory${currentLevel}`;
      currentPath[levelKey] = category.title || "";
      if (category[`newEfSubcategory${currentLevel + 1}s`]?.length) {
        hierarchyPath += hierarchyPath ? `-${category.id}` : `${category.id}`;

      }

      if (currentLevel === 4 || !category[`newEfSubcategory${currentLevel + 1}s`]?.length) {
        return [{...currentPath, hierarchyId: hierarchyPath}]; // Add hierarchyId to the result
      }

      const nextLevelKey = `newEfSubcategory${currentLevel + 1}s`;
      return category[nextLevelKey].flatMap((nextCategory: any) =>
        traverse(nextCategory, currentLevel + 1, {...currentPath}, hierarchyPath)
      );
    };

    const findCategoryAtLevel = (categories: any[], targetLevel: number, id: number, currentLevel = 1, parentPath: Record<string, string> = {}, hierarchyPath = "") => {
      if (currentLevel === targetLevel) {
        const foundCategory = categories.find((cat: any) => cat.id === id);
        if (foundCategory) {
          parentPath[`subCategory${currentLevel}`] = foundCategory.title;
          hierarchyPath += hierarchyPath ? `-${foundCategory.id}` : `${foundCategory.id}`;
          return {...parentPath, ...foundCategory, hierarchyPath};
        }
        return null;
      }

      const nextLevelKey = `newEfSubcategory${currentLevel + 1}s`;
      for (const category of categories) {
        if (category[nextLevelKey]?.length) {
          const updatedPath = {...parentPath, [`subCategory${currentLevel}`]: category.title};

          const newHierarchyPath = hierarchyPath ? `${hierarchyPath}-${category.id}` : `${category.id}`;
          const found: any = findCategoryAtLevel(category[nextLevelKey], targetLevel, id, currentLevel + 1, updatedPath, newHierarchyPath);
          if (found) return found;
        }
      }
      return null;
    };

    const getMergedDataForSelectedIds = async (selectedIds: string[]) => {
      const mergedResults: any[] = [];

      for (const selectedId of selectedIds) {
        const [level, id] = selectedId.split("-").map(Number);

        const rootCategory = findCategoryAtLevel(subCategory, level, id);
        if (!rootCategory) {
          return [];
        }

        const result = traverse(rootCategory, level, {
          subCategory1: rootCategory.subCategory1 || "",
          subCategory2: rootCategory.subCategory2 || "",
          subCategory3: rootCategory.subCategory3 || "",
          subCategory4: rootCategory.subCategory4 || ""
        }, rootCategory.hierarchyPath || "");

        const filteredResult = result.map((item: any) => {
          for (let i = 4; i >= 1; i--) {
            const levelKey = `subCategory${i}`;
            if (!item[levelKey]) {
              delete item[levelKey];
            }
          }
          return item;
        }).map((item: any) => {
          for (const key in item) {
            if (key.startsWith('subCategory') && typeof item[key] === 'object') {
              item[key] = item[key].title;
            }
          }
          return item;
        });

        mergedResults.push(...filteredResult);
      }

      return mergedResults;
    };

    return getMergedDataForSelectedIds(selectedIds);
  }
}
