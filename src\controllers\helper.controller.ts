import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  Response,
  RestBindings,
} from '@loopback/rest';
import * as ExcelJS from 'exceljs';
import {Helper} from '../models';
import {HelperRepository} from '../repositories';

export class HelperController {
  constructor(
    @repository(HelperRepository)
    public helperRepository: HelperRepository,
    @inject(RestBindings.Http.RESPONSE) private response: Response,
  ) { }

  @post('/helpers', {
    responses: {
      '200': {
        description: 'Helper model instance',
        content: {'application/json': {schema: getModelSchemaRef(Helper)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {
            title: '<PERSON>H<PERSON><PERSON>',
            exclude: ['id'],
          }),
        },
      },
    })
    helper: Omit<Helper, 'id'>,
  ): Promise<Helper> {
    return this.helperRepository.create(helper);
  }

  @get('/helpers/count', {
    responses: {
      '200': {
        description: 'Helper model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(Helper) where?: Where<Helper>,
  ): Promise<Count> {
    return this.helperRepository.count(where);
  }

  @get('/helpers', {
    responses: {
      '200': {
        description: 'Array of Helper model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Helper, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Helper) filter?: Filter<Helper>,
  ): Promise<Helper[]> {
    return this.helperRepository.find(filter);
  }

  @patch('/helpers', {
    responses: {
      '200': {
        description: 'Helper PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {partial: true}),
        },
      },
    })
    helper: Helper,
    @param.where(Helper) where?: Where<Helper>,
  ): Promise<Count> {
    return this.helperRepository.updateAll(helper, where);
  }

  @get('/helpers/{id}', {
    responses: {
      '200': {
        description: 'Helper model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Helper, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(Helper, {exclude: 'where'}) filter?: FilterExcludingWhere<Helper>
  ): Promise<Helper> {
    return this.helperRepository.findById(id, filter);
  }

  @patch('/helpers/{id}', {
    responses: {
      '204': {
        description: 'Helper PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {partial: true}),
        },
      },
    })
    helper: Helper,
  ): Promise<void> {
    await this.helperRepository.updateById(id, helper);
  }

  @put('/helpers/{id}', {
    responses: {
      '204': {
        description: 'Helper PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() helper: Helper,
  ): Promise<void> {
    await this.helperRepository.replaceById(id, helper);
  }

  @del('/helpers/{id}', {
    responses: {
      '204': {
        description: 'Helper DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.helperRepository.deleteById(id);
  }

  @post('/helpers/generate-excel', {
    responses: {
      '200': {
        description: 'Excel file generated successfully',
        content: {
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
            schema: {
              type: 'string',
              format: 'binary',
            },
          },
        },
      },
      '400': {
        description: 'Bad request - Invalid JSON data',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {type: 'boolean'},
                message: {type: 'string'},
              },
            },
          },
        },
      },
      '500': {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {type: 'boolean'},
                message: {type: 'string'},
              },
            },
          },
        },
      },
    },
  })
  async generateExcel(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              data: {
                type: 'array',
                items: {
                  type: 'object',
                },
                description: 'Array of objects to be converted to Excel',
              },
              sheetName: {
                type: 'string',
                description: 'Name of the Excel sheet (optional, defaults to "Sheet1")',
              },
              fileName: {
                type: 'string',
                description: 'Name of the Excel file (optional, defaults to "data.xlsx")',
              },
            },
            required: ['data'],
          },
        },
      },
    })
    requestData: {
      data: any[];
      sheetName?: string;
      fileName?: string;
    },
  ): Promise<void> {
    try {
      const {data, sheetName = 'Sheet1', fileName = 'data.xlsx'} = requestData;

      // Validate input data
      if (!Array.isArray(data) || data.length === 0) {
        this.response.status(400).json({
          status: false,
          message: 'Data must be a non-empty array',
        });
        return;
      }

      // Create a new workbook and worksheet
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(sheetName);

      // Get headers from the first object
      const headers = Object.keys(data[0]);

      // Add headers to the worksheet
      worksheet.addRow(headers);

      // Style the header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = {bold: true};
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: {argb: 'FFE0E0E0'},
      };

      // Add data rows
      data.forEach(item => {
        const row = headers.map(header => item[header] || '');
        worksheet.addRow(row);
      });

      // Auto-fit columns
      worksheet.columns.forEach((column: any) => {
        let maxLength = 0;
        column.eachCell?.({includeEmpty: true}, (cell: any) => {
          const columnLength = cell.value ? cell.value.toString().length : 10;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = maxLength < 10 ? 10 : maxLength + 2;
      });

      // Generate Excel buffer
      const buffer = await workbook.xlsx.writeBuffer();

      // Set response headers
      this.response.setHeader(
        'Content-Disposition',
        `attachment; filename="${fileName}"`,
      );
      this.response.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );

      // Send the Excel file
      this.response.send(buffer);
    } catch (error: any) {
      this.response.status(500).json({
        status: false,
        message: `Error generating Excel file: ${error.message}`,
      });
    }
  }
}
