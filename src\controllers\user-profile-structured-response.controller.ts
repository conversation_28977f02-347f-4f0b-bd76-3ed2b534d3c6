import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  StructuredResponse,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileStructuredResponseController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/structured-responses', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many StructuredResponse',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(StructuredResponse)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<StructuredResponse>,
  ): Promise<StructuredResponse[]> {
    return this.userProfileRepository.structuredResponses(id).find(filter);
  }

  @post('/user-profiles/{id}/structured-responses', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(StructuredResponse)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StructuredResponse, {
            title: 'NewStructuredResponseInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) structuredResponse: Omit<StructuredResponse, 'id'>,
  ): Promise<StructuredResponse> {
    return this.userProfileRepository.structuredResponses(id).create(structuredResponse);
  }

  @patch('/user-profiles/{id}/structured-responses', {
    responses: {
      '200': {
        description: 'UserProfile.StructuredResponse PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StructuredResponse, {partial: true}),
        },
      },
    })
    structuredResponse: Partial<StructuredResponse>,
    @param.query.object('where', getWhereSchemaFor(StructuredResponse)) where?: Where<StructuredResponse>,
  ): Promise<Count> {
    if (where) {
      return this.userProfileRepository.structuredResponses(id).patch(structuredResponse, where);

    } else {
      return {count: 0}
    }
  }

  @del('/user-profiles/{id}/structured-responses', {
    responses: {
      '200': {
        description: 'UserProfile.StructuredResponse DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(StructuredResponse)) where?: Where<StructuredResponse>,
  ): Promise<Count> {
    if (where) {
      return this.userProfileRepository.structuredResponses(id).delete(where);

    } else {
      return {count: 0}
    }
  }
}
